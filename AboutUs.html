<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Home</title>
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="description" content="" />
  <link rel="icon" href="favicon.png">
  <link rel="stylesheet" href="Home.css">

  <script src="https://cdn.jsdelivr.net/npm/d3@7"></script>

</head>
<body style="display: flex; flex-direction: column;">
  <nav class="nav" id="nav">
    <a href="Home.html"><img src="C:\Users\<USER>\Desktop\T01\PowerIcon.png" width="50" height="50"></a>
    <a href="Home.html">Home</a>
    <a href="Televisions.html">Televisions</a>
    <a href="AboutUs.html" style="background-color: gold;">About Us</a>
    <a href="javascript:void(0);" id="menu-toggle" onclick="togglemenu()">
      <div class="menu-toggle__bar1"></div>
      <div class="menu-toggle_x_bar2"></div>
      <div class="menu-toggle__bar3"></div>
    </a>
  </nav>

  <h1>This is the about page</h1>
  <div>

  </div>
  <h1>This is a website about televisions</h1>
  <div style="font-size: 4em;"> The Beatles were an English rock band formed in Liverpool in 1960. The core lineup of the band comprised John Lennon, Paul McCartney, George Harrison and Ringo Starr. They are widely regarded as the most influential band in Western popular music and were integral to the development of 1960s counterculture and the recognition of popular music as an art form. Rooted in skiffle, beat and 1950s rock 'n' roll, their sound incorporated elements of classical music and traditional pop in innovative ways..</div>

  <footer>
    <small>© <script>document.write(new Date().getFullYear())</script> Shamil Haqeem bin Shukarmin, GenAI is used to write the commit messages on GitHub, and write Javascript for navigation.</small>
  </footer>
  
  <script>
  function togglemenu() {
    var x = document.getElementById("nav");
    if (x.className === "nav") {
      x.className += " nav--open";
    } else {
      x.className = "nav";
    }
    var element = document.getElementById("menu-toggle");
    element.classList.toggle("menu-toggle--open");
  }
  </script>
</body>
</html>